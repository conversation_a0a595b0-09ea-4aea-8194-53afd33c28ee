﻿2025-08-22 21:00:41 173
日志类型：操作日志
详情：//+-------------------------------------------------------------------+
//+ 异常信息
//+-------------------------------------------------------------------+
----异常抛出时间2025年8月22日 21:00:22
----异常由System.Windows.Forms抛出
----抛出异常的方法是：SetToolInfo
----错误信息：将提示添加到本机工具提示控件失败。
----详细信息：   在 System.Windows.Forms.ToolTip.SetToolInfo(Control ctl, String caption)

   在 System.Windows.Forms.ToolTip.CreateRegion(Control ctl)

   在 System.Windows.Forms.ToolTip.CreateAllRegions()

   在 System.Windows.Forms.ToolTip.get_TopLevelControl()

   在 System.Windows.Forms.ToolTip.CreateRegion(Control ctl)

   在 System.Windows.Forms.ToolTip.HandleCreated(Object sender, EventArgs eventargs)

   在 System.Windows.Forms.Control.OnHandleCreated(EventArgs e)

   在 System.Windows.Forms.Control.WmCreate(Message& m)

   在 System.Windows.Forms.Control.WndProc(Message& m)

   在 System.Windows.Forms.ButtonBase.WndProc(Message& m)

   在 System.Windows.Forms.Button.WndProc(Message& m)

   在 System.Windows.Forms.NativeWindow.Callback(IntPtr hWnd, Int32 msg, IntPtr wparam, IntPtr lparam)
System.InvalidOperationException
将提示添加到本机工具提示控件失败。

----所有内部异常结束

//+-------------------------------------------------------------------+
//+ 调用堆栈
//+-------------------------------------------------------------------+
   在 System.Windows.Forms.ToolTip.SetToolInfo(Control ctl, String caption)

   在 System.Windows.Forms.ToolTip.CreateRegion(Control ctl)

   在 System.Windows.Forms.ToolTip.CreateAllRegions()

   在 System.Windows.Forms.ToolTip.get_TopLevelControl()

   在 System.Windows.Forms.ToolTip.CreateRegion(Control ctl)

   在 System.Windows.Forms.ToolTip.HandleCreated(Object sender, EventArgs eventargs)

   在 System.Windows.Forms.Control.OnHandleCreated(EventArgs e)

   在 System.Windows.Forms.Control.WmCreate(Message& m)

   在 System.Windows.Forms.Control.WndProc(Message& m)

   在 System.Windows.Forms.ButtonBase.WndProc(Message& m)

   在 System.Windows.Forms.Button.WndProc(Message& m)

   在 System.Windows.Forms.NativeWindow.Callback(IntPtr hWnd, Int32 msg, IntPtr wparam, IntPtr lparam)
----调用堆栈结束

//+-------------------------------------------------------------------+
//+ 已装载的Assembly
//+-------------------------------------------------------------------+
mscorlib
Version:	*******
CodeBase:file:///C:/Windows/Microsoft.NET/Framework64/v4.0.30319/mscorlib.dll
FullName:mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089

OCR Recognition Assistant
Version:	*******
CodeBase:file:///D:/Code/CatchTools/bin/Debug/OCR Recognition Assistant.exe
FullName:OCR Recognition Assistant, Version=*******, Culture=neutral, PublicKeyToken=null

System.Windows.Forms
Version:	*******
CodeBase:file:///C:/Windows/Microsoft.Net/assembly/GAC_MSIL/System.Windows.Forms/v4.0_*******__b77a5c561934e089/System.Windows.Forms.dll
FullName:System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089

System
Version:	*******
CodeBase:file:///C:/Windows/Microsoft.Net/assembly/GAC_MSIL/System/v4.0_*******__b77a5c561934e089/System.dll
FullName:System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089

System.Drawing
Version:	*******
CodeBase:file:///C:/Windows/Microsoft.Net/assembly/GAC_MSIL/System.Drawing/v4.0_*******__b03f5f7f11d50a3a/System.Drawing.dll
FullName:System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a

System.Web.Extensions
Version:	*******
CodeBase:file:///C:/Windows/Microsoft.Net/assembly/GAC_MSIL/System.Web.Extensions/v4.0_*******__31bf3856ad364e35/System.Web.Extensions.dll
FullName:System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35

System.Core
Version:	*******
CodeBase:file:///C:/Windows/Microsoft.Net/assembly/GAC_MSIL/System.Core/v4.0_*******__b77a5c561934e089/System.Core.dll
FullName:System.Core, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089

System.Configuration
Version:	*******
CodeBase:file:///C:/Windows/Microsoft.Net/assembly/GAC_MSIL/System.Configuration/v4.0_*******__b03f5f7f11d50a3a/System.Configuration.dll
FullName:System.Configuration, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a

System.Xml
Version:	*******
CodeBase:file:///C:/Windows/Microsoft.Net/assembly/GAC_MSIL/System.Xml/v4.0_*******__b77a5c561934e089/System.Xml.dll
FullName:System.Xml, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089

Microsoft.ApplicationInsights
Version:	2.4.0.0
CodeBase:file:///D:/Code/CatchTools/bin/Debug/OCR Recognition Assistant.exe
FullName:Microsoft.ApplicationInsights, Version=2.4.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35

mscorlib.resources
Version:	*******
CodeBase:file:///C:/Windows/Microsoft.Net/assembly/GAC_MSIL/mscorlib.resources/v4.0_*******_zh-Hans_b77a5c561934e089/mscorlib.resources.dll
FullName:mscorlib.resources, Version=*******, Culture=zh-Hans, PublicKeyToken=b77a5c561934e089

System.Web
Version:	*******
CodeBase:file:///C:/Windows/Microsoft.Net/assembly/GAC_64/System.Web/v4.0_*******__b03f5f7f11d50a3a/System.Web.dll
FullName:System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a

System.Xml.Linq
Version:	*******
CodeBase:file:///C:/Windows/Microsoft.Net/assembly/GAC_MSIL/System.Xml.Linq/v4.0_*******__b77a5c561934e089/System.Xml.Linq.dll
FullName:System.Xml.Linq, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089

System.Management
Version:	*******
CodeBase:file:///C:/Windows/Microsoft.Net/assembly/GAC_MSIL/System.Management/v4.0_*******__b03f5f7f11d50a3a/System.Management.dll
FullName:System.Management, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a

Interop.UIAutomationClient
Version:	1.0.0.0
CodeBase:file:///D:/Code/CatchTools/bin/Debug/OCR Recognition Assistant.exe
FullName:Interop.UIAutomationClient, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null

Accessibility
Version:	*******
CodeBase:file:///C:/Windows/Microsoft.Net/assembly/GAC_MSIL/Accessibility/v4.0_*******__b03f5f7f11d50a3a/Accessibility.dll
FullName:Accessibility, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a

System.Data
Version:	*******
CodeBase:file:///C:/Windows/Microsoft.Net/assembly/GAC_64/System.Data/v4.0_*******__b77a5c561934e089/System.Data.dll
FullName:System.Data, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089

System.Numerics
Version:	*******
CodeBase:file:///C:/Windows/Microsoft.Net/assembly/GAC_MSIL/System.Numerics/v4.0_*******__b77a5c561934e089/System.Numerics.dll
FullName:System.Numerics, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089

Microsoft.VisualStudio.Debugger.Runtime.Desktop
Version:	17.0.0.0
CodeBase:file:///C:/Program Files/Microsoft Visual Studio/2022/Enterprise/Common7/IDE/PrivateAssemblies/Runtime/Microsoft.VisualStudio.Debugger.Runtime.Desktop.dll
FullName:Microsoft.VisualStudio.Debugger.Runtime.Desktop, Version=17.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a

System.resources
Version:	*******
CodeBase:file:///C:/Windows/Microsoft.Net/assembly/GAC_MSIL/System.resources/v4.0_*******_zh-Hans_b77a5c561934e089/System.resources.dll
FullName:System.resources, Version=*******, Culture=zh-Hans, PublicKeyToken=b77a5c561934e089

System.Windows.Forms.resources
Version:	*******
CodeBase:file:///C:/Windows/Microsoft.Net/assembly/GAC_MSIL/System.Windows.Forms.resources/v4.0_*******_zh-Hans_b77a5c561934e089/System.Windows.Forms.resources.dll
FullName:System.Windows.Forms.resources, Version=*******, Culture=zh-Hans, PublicKeyToken=b77a5c561934e089


----Assembly结束


----------------------------------------
